import SEO from "@/components/SEO";
import sustainImg from "@/assets/sustainability-green-plant.jpg";
import { Leaf } from "lucide-react";

export default function Sustainability() {
  return (
    <main className="container py-12">
      <SEO
        title="Sustainability | Manechem"
        description="Lower-carbon logistics, returnable packaging, waste minimization, and supplier stewardship."
        canonical="/sustainability"
      />
      <h1 className="mb-6 text-4xl font-bold">Sustainability</h1>
      <img src={sustainImg} alt="Green leaves silhouette over modern chemical plant" className="mb-8 w-full rounded-lg border shadow-elevated" loading="lazy" />
      <div className="grid gap-6 md:grid-cols-3">
        {[
          { title: 'Returnable IBC Programs', desc: 'Closed-loop totes and drums reduce waste and improve safety.' },
          { title: 'Route Optimization', desc: 'Network design to cut empty miles and emissions.' },
          { title: 'Supplier Stewardship', desc: 'Partnering with producers on greener chemistries and energy use.' },
        ].map((item) => (
          <article key={item.title} className="rounded-lg border p-6">
            <h2 className="flex items-center gap-2 text-lg font-semibold"><Leaf className="text-primary" /> {item.title}</h2>
            <p className="mt-2 text-sm text-muted-foreground">{item.desc}</p>
          </article>
        ))}
      </div>
    </main>
  );
}
