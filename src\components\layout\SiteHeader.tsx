import { Link, NavLink } from "react-router-dom";
import { useState } from "react";
import { Menu, X, LogIn, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";

const navItems = [
  { to: "/", label: "Home" },
  { to: "/products", label: "Products" },
  { to: "/industries", label: "Industries" },
  { to: "/services", label: "Services" },
  { to: "/quality", label: "Quality" },
  { to: "/sustainability", label: "Sustainability" },
  { to: "/sds", label: "SDS" },
  { to: "/about", label: "About" },
  { to: "/contact", label: "Contact" },
  { to: "/dashboard", label: "Business System" },
];

export default function SiteHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, userProfile } = useAuth();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <Link to="/" className="flex items-center gap-3" aria-label="Manechem Home">
          <img
            src="/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png"
            alt="Manechem logo - orange arc with wordmark"
            className="h-8 w-auto"
            loading="lazy"
            decoding="async"
          />
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6" aria-label="Primary">
          {navItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `text-sm transition-colors hover:text-primary ${isActive ? "text-primary font-medium" : "text-muted-foreground"}`
              }
            >
              {item.label}
            </NavLink>
          ))}
        </nav>

        <div className="flex items-center gap-4">
          <div className="hidden md:flex items-center gap-2">
            {user && userProfile ? (
              // Authenticated user - show dashboard link
              <>
                <Link to="/dashboard">
                  <Button variant="outline" size="sm" className="shadow-md hover:shadow-lg transition-all duration-300">
                    <Settings className="h-4 w-4 mr-2" />
                    Dashboard
                  </Button>
                </Link>
                <span className="text-sm text-muted-foreground">
                  {userProfile.full_name}
                </span>
              </>
            ) : (
              // Not authenticated - show login and quote buttons
              <>
                <Link to="/contact">
                  <Button variant="outline" size="sm" className="shadow-md hover:shadow-lg transition-all duration-300">
                    Request Quote
                  </Button>
                </Link>
                <Link to="/auth">
                  <Button variant="hero" size="sm" className="shadow-md hover:shadow-lg transition-all duration-300">
                    <LogIn className="h-4 w-4 mr-2" />
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 hover:bg-accent rounded-md transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t bg-background/95 backdrop-blur">
          <nav className="container py-4 space-y-2" aria-label="Mobile">
            {navItems.map((item) => (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) =>
                  `block px-4 py-2 text-sm rounded-md transition-colors ${
                    isActive
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:text-primary hover:bg-accent"
                  }`
                }
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </NavLink>
            ))}
            <div className="pt-2 px-4 space-y-2">
              {user && userProfile ? (
                // Authenticated user - show dashboard and user info
                <>
                  <Link to="/dashboard" onClick={() => setIsMenuOpen(false)}>
                    <Button variant="hero" className="w-full">
                      <Settings className="h-4 w-4 mr-2" />
                      Dashboard
                    </Button>
                  </Link>
                  <div className="text-center text-sm text-muted-foreground py-2">
                    Signed in as {userProfile.full_name}
                  </div>
                </>
              ) : (
                // Not authenticated - show login and quote buttons
                <>
                  <Link to="/contact" onClick={() => setIsMenuOpen(false)}>
                    <Button variant="outline" className="w-full">
                      Request Quote
                    </Button>
                  </Link>
                  <Link to="/auth" onClick={() => setIsMenuOpen(false)}>
                    <Button variant="hero" className="w-full">
                      <LogIn className="h-4 w-4 mr-2" />
                      Sign In
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
