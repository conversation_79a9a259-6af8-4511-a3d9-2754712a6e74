import { useMemo, useState } from "react";
import SEO from "@/components/SEO";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const SDS_ITEMS = [
  { name: 'Acetone', version: 'v7.2', lang: 'EN' },
  { name: 'Isopropyl Alcohol', version: 'v5.9', lang: 'EN' },
  { name: 'Ethanol 96%', version: 'v4.1', lang: 'EN' },
  { name: 'Sodium Hydroxide 50%', version: 'v6.0', lang: 'EN' },
  { name: 'Hydrochloric Acid 32%', version: 'v3.4', lang: 'EN' },
];

export default function SDS() {
  const [q, setQ] = useState('');
  const filtered = useMemo(() => SDS_ITEMS.filter(i => i.name.toLowerCase().includes(q.toLowerCase())), [q]);

  return (
    <main className="container py-12">
      <SEO
        title="SDS Library | Manechem"
        description="Quickly search and download Safety Data Sheets (SDS) for our stocked products."
        canonical="/sds"
      />
      <h1 className="mb-6 text-4xl font-bold">SDS Library</h1>
      <div className="mb-6 max-w-md">
        <Input value={q} onChange={(e) => setQ(e.target.value)} placeholder="Search chemicals (e.g., Acetone)" aria-label="Search SDS" />
      </div>
      <div className="grid gap-4">
        {filtered.map((i) => (
          <Card key={i.name}>
            <CardHeader>
              <CardTitle className="text-lg">{i.name}</CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{i.lang} · {i.version}</span>
              <a href="#" className="text-primary underline-offset-4 hover:underline">Download</a>
            </CardContent>
          </Card>
        ))}
        {filtered.length === 0 && (
          <p className="text-sm text-muted-foreground">No matches found.</p>
        )}
      </div>
    </main>
  );
}
