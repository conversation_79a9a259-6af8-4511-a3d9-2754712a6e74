import { useAuth } from '@/contexts/AuthContext';
import SEO from '@/components/SEO';
import DemoNotice from '@/components/DemoNotice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Receipt,
  ShoppingCart,
  CreditCard,
  Package,
  Users,
  Building2,
  TrendingUp,
  LogOut
} from 'lucide-react';

export default function Dashboard() {
  const { userProfile, company, signOut } = useAuth();

  // Demo mode when database is not configured
  const isDemoMode = !import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY === 'your-anon-key-here';

  const quickActions = [
    {
      title: 'Create Quotation',
      description: 'Generate a new quotation for customers',
      icon: FileText,
      href: '/quotations/new',
      permission: 'create_quotations',
    },
    {
      title: 'Create Invoice',
      description: 'Issue a new invoice',
      icon: Receipt,
      href: '/invoices/new',
      permission: 'create_invoices',
    },
    {
      title: 'Create Purchase Order',
      description: 'Create a new purchase order',
      icon: ShoppingCart,
      href: '/purchase-orders/new',
      permission: 'create_purchase_orders',
    },
    {
      title: 'Payment Voucher',
      description: 'Process supplier payments',
      icon: CreditCard,
      href: '/payment-vouchers/new',
      permission: 'create_payment_vouchers',
    },
    {
      title: 'Goods Received',
      description: 'Record received goods',
      icon: Package,
      href: '/grv/new',
      permission: 'create_grv',
    },
    {
      title: 'Manage Customers',
      description: 'View and manage customers',
      icon: Users,
      href: '/customers',
      permission: 'view_customers',
    },
  ];

  const stats = [
    {
      title: 'Total Customers',
      value: '0',
      icon: Users,
      description: 'Active customers',
    },
    {
      title: 'Pending Invoices',
      value: '0',
      icon: Receipt,
      description: 'Awaiting payment',
    },
    {
      title: 'Open Quotations',
      value: '0',
      icon: FileText,
      description: 'Pending approval',
    },
    {
      title: 'Monthly Revenue',
      value: '$0',
      icon: TrendingUp,
      description: 'This month',
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="Dashboard | Manechem"
        description="Manechem business management dashboard"
        canonical="/dashboard"
      />
      
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <img
              src="/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png"
              alt="Manechem logo"
              className="h-8 w-auto"
            />
            <div>
              <h1 className="text-xl font-semibold">Dashboard</h1>
              <p className="text-sm text-muted-foreground">
                {isDemoMode ? 'Demo Mode - Database Not Connected' : `Welcome back, ${userProfile?.full_name || 'User'}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm font-medium">{isDemoMode ? 'Manechem (Demo)' : company?.name || 'Company'}</p>
              <p className="text-xs text-muted-foreground capitalize">
                {isDemoMode ? 'Demo User' : userProfile?.role || 'User'}
              </p>
            </div>
            {!isDemoMode && (
              <Button variant="outline" size="sm" onClick={signOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            )}
          </div>
        </div>
      </header>

      <main className="container py-8">
        {/* Demo Notice */}
        <DemoNotice />

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div className="md:col-span-2 lg:col-span-2">
            <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
            <div className="grid gap-4 sm:grid-cols-2">
              {quickActions.map((action) => (
                <Card key={action.title} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <action.icon className="h-5 w-5 text-primary" />
                      {action.title}
                    </CardTitle>
                    <CardDescription>
                      {action.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Activity Feed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="text-sm">
                      <p className="font-medium">System initialized</p>
                      <p className="text-muted-foreground">Welcome to Manechem!</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
