import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Products from "./pages/Products";
import Industries from "./pages/Industries";
import Services from "./pages/Services";
import Quality from "./pages/Quality";
import Sustainability from "./pages/Sustainability";
import SDS from "./pages/SDS";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Auth from "./pages/Auth";
import Dashboard from "./pages/Dashboard";
import SiteHeader from "./components/layout/SiteHeader";
import SiteFooter from "./components/layout/SiteFooter";
import ProtectedRoute from "./components/auth/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <HelmetProvider>
      <TooltipProvider>
        <AuthProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true,
            }}
          >
            <Routes>
              {/* Public routes */}
              <Route path="/" element={
                <>
                  <SiteHeader />
                  <Index />
                  <SiteFooter />
                </>
              } />
              <Route path="/products" element={
                <>
                  <SiteHeader />
                  <Products />
                  <SiteFooter />
                </>
              } />
              <Route path="/industries" element={
                <>
                  <SiteHeader />
                  <Industries />
                  <SiteFooter />
                </>
              } />
              <Route path="/services" element={
                <>
                  <SiteHeader />
                  <Services />
                  <SiteFooter />
                </>
              } />
              <Route path="/quality" element={
                <>
                  <SiteHeader />
                  <Quality />
                  <SiteFooter />
                </>
              } />
              <Route path="/sustainability" element={
                <>
                  <SiteHeader />
                  <Sustainability />
                  <SiteFooter />
                </>
              } />
              <Route path="/sds" element={
                <>
                  <SiteHeader />
                  <SDS />
                  <SiteFooter />
                </>
              } />
              <Route path="/about" element={
                <>
                  <SiteHeader />
                  <About />
                  <SiteFooter />
                </>
              } />
              <Route path="/contact" element={
                <>
                  <SiteHeader />
                  <Contact />
                  <SiteFooter />
                </>
              } />

              {/* Authentication route */}
              <Route path="/auth" element={<Auth />} />

              {/* Protected routes */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } />

              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </HelmetProvider>
  </QueryClientProvider>
);

export default App;
