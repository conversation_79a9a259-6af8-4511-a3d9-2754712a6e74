import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://ruivinhiknpymsvdeyyw.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'demo-key';

// For development, we'll create a client even with demo credentials
// In production, you should always have proper credentials
if (!supabaseUrl) {
  console.error('VITE_SUPABASE_URL is required');
}

if (!import.meta.env.VITE_SUPABASE_ANON_KEY) {
  console.warn('VITE_SUPABASE_ANON_KEY is missing. Using demo configuration.');
  console.warn('Please set up your Supabase project and add the environment variables.');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types
export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string;
          name: string;
          registration_number?: string;
          tax_number?: string;
          address?: string;
          city?: string;
          country?: string;
          postal_code?: string;
          phone?: string;
          email?: string;
          website?: string;
          logo_url?: string;
          currency: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['companies']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['companies']['Insert']>;
      };
      users: {
        Row: {
          id: string;
          company_id: string;
          email: string;
          full_name: string;
          role: 'admin' | 'manager' | 'accountant' | 'sales' | 'procurement' | 'viewer';
          phone?: string;
          is_active: boolean;
          last_login?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['users']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['users']['Insert']>;
      };
      customers: {
        Row: {
          id: string;
          company_id: string;
          name: string;
          contact_person?: string;
          email?: string;
          phone?: string;
          address?: string;
          city?: string;
          country?: string;
          postal_code?: string;
          tax_number?: string;
          credit_limit: number;
          payment_terms: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['customers']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['customers']['Insert']>;
      };
      suppliers: {
        Row: {
          id: string;
          company_id: string;
          name: string;
          contact_person?: string;
          email?: string;
          phone?: string;
          address?: string;
          city?: string;
          country?: string;
          postal_code?: string;
          tax_number?: string;
          payment_terms: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['suppliers']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['suppliers']['Insert']>;
      };
      products: {
        Row: {
          id: string;
          company_id: string;
          code: string;
          name: string;
          description?: string;
          category?: string;
          unit_of_measure: string;
          cost_price?: number;
          selling_price?: number;
          stock_quantity: number;
          minimum_stock: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['products']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['products']['Insert']>;
      };
      quotations: {
        Row: {
          id: string;
          company_id: string;
          quotation_number: string;
          customer_id?: string;
          customer_name: string;
          customer_email?: string;
          customer_phone?: string;
          customer_address?: string;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
          quotation_date: string;
          valid_until?: string;
          subtotal: number;
          tax_rate: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          notes?: string;
          terms_conditions?: string;
          created_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['quotations']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['quotations']['Insert']>;
      };
      invoices: {
        Row: {
          id: string;
          company_id: string;
          invoice_number: string;
          quotation_id?: string;
          customer_id?: string;
          customer_name: string;
          customer_email?: string;
          customer_phone?: string;
          customer_address?: string;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
          payment_status: 'pending' | 'partial' | 'paid' | 'overdue' | 'cancelled';
          invoice_date: string;
          due_date: string;
          subtotal: number;
          tax_rate: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          paid_amount: number;
          balance_due: number;
          notes?: string;
          terms_conditions?: string;
          created_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['invoices']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['invoices']['Insert']>;
      };
      purchase_orders: {
        Row: {
          id: string;
          company_id: string;
          po_number: string;
          supplier_id?: string;
          supplier_name: string;
          supplier_email?: string;
          supplier_phone?: string;
          supplier_address?: string;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
          order_date: string;
          expected_delivery_date?: string;
          subtotal: number;
          tax_rate: number;
          tax_amount: number;
          total_amount: number;
          notes?: string;
          terms_conditions?: string;
          created_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['purchase_orders']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['purchase_orders']['Insert']>;
      };
      payment_vouchers: {
        Row: {
          id: string;
          company_id: string;
          voucher_number: string;
          supplier_id?: string;
          purchase_order_id?: string;
          supplier_name: string;
          payment_method: string;
          reference_number?: string;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
          payment_date: string;
          amount: number;
          description?: string;
          notes?: string;
          created_by?: string;
          approved_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['payment_vouchers']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['payment_vouchers']['Insert']>;
      };
      goods_received_vouchers: {
        Row: {
          id: string;
          company_id: string;
          grv_number: string;
          purchase_order_id?: string;
          supplier_id?: string;
          supplier_name: string;
          delivery_note_number?: string;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
          received_date: string;
          received_by: string;
          notes?: string;
          created_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: Omit<Database['public']['Tables']['goods_received_vouchers']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['goods_received_vouchers']['Insert']>;
      };
    };
  };
}

// Export typed supabase client
export type SupabaseClient = typeof supabase;
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
