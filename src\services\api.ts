import { supabase, Tables, InsertTables, UpdateTables } from '@/lib/supabase';

// Base API service class
class BaseApiService<T extends keyof Tables> {
  constructor(private tableName: T) {}

  async getAll(filters?: Record<string, any>) {
    let query = supabase.from(this.tableName).select('*');
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as Tables[T][];
  }

  async getById(id: string) {
    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data as Tables[T];
  }

  async create(data: InsertTables[T]) {
    const { data: result, error } = await supabase
      .from(this.tableName)
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result as Tables[T];
  }

  async update(id: string, data: UpdateTables[T]) {
    const { data: result, error } = await supabase
      .from(this.tableName)
      .update(data)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return result as Tables[T];
  }

  async delete(id: string) {
    const { error } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    return true;
  }
}

// Specific service classes
export class CompanyService extends BaseApiService<'companies'> {
  constructor() {
    super('companies');
  }

  async getCurrentCompany() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data: userProfile } = await supabase
      .from('users')
      .select('company_id')
      .eq('id', user.id)
      .single();

    if (!userProfile?.company_id) throw new Error('No company associated with user');

    return this.getById(userProfile.company_id);
  }
}

export class UserService extends BaseApiService<'users'> {
  constructor() {
    super('users');
  }

  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    return this.getById(user.id);
  }

  async updateCurrentUser(data: UpdateTables['users']) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    return this.update(user.id, data);
  }
}

export class CustomerService extends BaseApiService<'customers'> {
  constructor() {
    super('customers');
  }

  async searchCustomers(query: string) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .or(`name.ilike.%${query}%,email.ilike.%${query}%,contact_person.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data;
  }
}

export class SupplierService extends BaseApiService<'suppliers'> {
  constructor() {
    super('suppliers');
  }

  async searchSuppliers(query: string) {
    const { data, error } = await supabase
      .from('suppliers')
      .select('*')
      .or(`name.ilike.%${query}%,email.ilike.%${query}%,contact_person.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data;
  }
}

export class ProductService extends BaseApiService<'products'> {
  constructor() {
    super('products');
  }

  async searchProducts(query: string) {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .or(`name.ilike.%${query}%,code.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data;
  }

  async updateStock(productId: string, quantity: number, operation: 'add' | 'subtract') {
    const product = await this.getById(productId);
    const newQuantity = operation === 'add' 
      ? product.stock_quantity + quantity 
      : product.stock_quantity - quantity;

    if (newQuantity < 0) {
      throw new Error('Insufficient stock');
    }

    return this.update(productId, { stock_quantity: newQuantity });
  }
}

export class QuotationService extends BaseApiService<'quotations'> {
  constructor() {
    super('quotations');
  }

  async getWithItems(id: string) {
    const { data, error } = await supabase
      .from('quotations')
      .select(`
        *,
        quotation_items (*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  async createWithItems(quotation: InsertTables['quotations'], items: InsertTables['quotation_items'][]) {
    const { data: newQuotation, error: quotationError } = await supabase
      .from('quotations')
      .insert(quotation)
      .select()
      .single();

    if (quotationError) throw quotationError;

    const itemsWithQuotationId = items.map(item => ({
      ...item,
      quotation_id: newQuotation.id
    }));

    const { error: itemsError } = await supabase
      .from('quotation_items')
      .insert(itemsWithQuotationId);

    if (itemsError) throw itemsError;

    return this.getWithItems(newQuotation.id);
  }

  async getNextNumber() {
    const { data, error } = await supabase.rpc('get_next_document_number', {
      p_company_id: (await new CompanyService().getCurrentCompany()).id,
      p_document_type: 'quotation'
    });

    if (error) throw error;
    return data;
  }
}

export class InvoiceService extends BaseApiService<'invoices'> {
  constructor() {
    super('invoices');
  }

  async getWithItems(id: string) {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  async createWithItems(invoice: InsertTables['invoices'], items: InsertTables['invoice_items'][]) {
    const { data: newInvoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert(invoice)
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    const itemsWithInvoiceId = items.map(item => ({
      ...item,
      invoice_id: newInvoice.id
    }));

    const { error: itemsError } = await supabase
      .from('invoice_items')
      .insert(itemsWithInvoiceId);

    if (itemsError) throw itemsError;

    return this.getWithItems(newInvoice.id);
  }

  async getNextNumber() {
    const { data, error } = await supabase.rpc('get_next_document_number', {
      p_company_id: (await new CompanyService().getCurrentCompany()).id,
      p_document_type: 'invoice'
    });

    if (error) throw error;
    return data;
  }

  async recordPayment(invoiceId: string, amount: number) {
    const invoice = await this.getById(invoiceId);
    const newPaidAmount = invoice.paid_amount + amount;
    const newBalanceDue = invoice.total_amount - newPaidAmount;
    
    let paymentStatus: 'pending' | 'partial' | 'paid' | 'overdue' | 'cancelled' = 'pending';
    
    if (newPaidAmount >= invoice.total_amount) {
      paymentStatus = 'paid';
    } else if (newPaidAmount > 0) {
      paymentStatus = 'partial';
    }

    return this.update(invoiceId, {
      paid_amount: newPaidAmount,
      balance_due: newBalanceDue,
      payment_status: paymentStatus
    });
  }
}

export class PurchaseOrderService extends BaseApiService<'purchase_orders'> {
  constructor() {
    super('purchase_orders');
  }

  async getWithItems(id: string) {
    const { data, error } = await supabase
      .from('purchase_orders')
      .select(`
        *,
        purchase_order_items (*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  async createWithItems(purchaseOrder: InsertTables['purchase_orders'], items: InsertTables['purchase_order_items'][]) {
    const { data: newPO, error: poError } = await supabase
      .from('purchase_orders')
      .insert(purchaseOrder)
      .select()
      .single();

    if (poError) throw poError;

    const itemsWithPOId = items.map(item => ({
      ...item,
      purchase_order_id: newPO.id
    }));

    const { error: itemsError } = await supabase
      .from('purchase_order_items')
      .insert(itemsWithPOId);

    if (itemsError) throw itemsError;

    return this.getWithItems(newPO.id);
  }

  async getNextNumber() {
    const { data, error } = await supabase.rpc('get_next_document_number', {
      p_company_id: (await new CompanyService().getCurrentCompany()).id,
      p_document_type: 'purchase_order'
    });

    if (error) throw error;
    return data;
  }
}

export class PaymentVoucherService extends BaseApiService<'payment_vouchers'> {
  constructor() {
    super('payment_vouchers');
  }

  async getNextNumber() {
    const { data, error } = await supabase.rpc('get_next_document_number', {
      p_company_id: (await new CompanyService().getCurrentCompany()).id,
      p_document_type: 'payment_voucher'
    });

    if (error) throw error;
    return data;
  }

  async approve(id: string, approvedBy: string) {
    return this.update(id, {
      status: 'approved',
      approved_by: approvedBy
    });
  }
}

export class GRVService extends BaseApiService<'goods_received_vouchers'> {
  constructor() {
    super('goods_received_vouchers');
  }

  async getWithItems(id: string) {
    const { data, error } = await supabase
      .from('goods_received_vouchers')
      .select(`
        *,
        grv_items (*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  async createWithItems(grv: InsertTables['goods_received_vouchers'], items: InsertTables['grv_items'][]) {
    const { data: newGRV, error: grvError } = await supabase
      .from('goods_received_vouchers')
      .insert(grv)
      .select()
      .single();

    if (grvError) throw grvError;

    const itemsWithGRVId = items.map(item => ({
      ...item,
      grv_id: newGRV.id
    }));

    const { error: itemsError } = await supabase
      .from('grv_items')
      .insert(itemsWithGRVId);

    if (itemsError) throw itemsError;

    // Update received quantities in purchase order items
    for (const item of items) {
      if (item.purchase_order_item_id) {
        const { data: poItem } = await supabase
          .from('purchase_order_items')
          .select('received_quantity')
          .eq('id', item.purchase_order_item_id)
          .single();

        if (poItem) {
          await supabase
            .from('purchase_order_items')
            .update({
              received_quantity: poItem.received_quantity + item.received_quantity
            })
            .eq('id', item.purchase_order_item_id);
        }
      }

      // Update product stock if applicable
      if (item.product_id && item.condition === 'good') {
        await productService.updateStock(item.product_id, item.received_quantity, 'add');
      }
    }

    return this.getWithItems(newGRV.id);
  }

  async getNextNumber() {
    const { data, error } = await supabase.rpc('get_next_document_number', {
      p_company_id: (await new CompanyService().getCurrentCompany()).id,
      p_document_type: 'grv'
    });

    if (error) throw error;
    return data;
  }
}

// Document item services
export class QuotationItemService extends BaseApiService<'quotation_items'> {
  constructor() {
    super('quotation_items');
  }
}

export class InvoiceItemService extends BaseApiService<'invoice_items'> {
  constructor() {
    super('invoice_items');
  }
}

export class PurchaseOrderItemService extends BaseApiService<'purchase_order_items'> {
  constructor() {
    super('purchase_order_items');
  }
}

export class GRVItemService extends BaseApiService<'grv_items'> {
  constructor() {
    super('grv_items');
  }
}

// Export service instances
export const companyService = new CompanyService();
export const userService = new UserService();
export const customerService = new CustomerService();
export const supplierService = new SupplierService();
export const productService = new ProductService();
export const quotationService = new QuotationService();
export const invoiceService = new InvoiceService();
export const purchaseOrderService = new PurchaseOrderService();
export const paymentVoucherService = new PaymentVoucherService();
export const grvService = new GRVService();
export const quotationItemService = new QuotationItemService();
export const invoiceItemService = new InvoiceItemService();
export const purchaseOrderItemService = new PurchaseOrderItemService();
export const grvItemService = new GRVItemService();
