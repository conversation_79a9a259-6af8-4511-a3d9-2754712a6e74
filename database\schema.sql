-- Manechem Business Management System Database Schema
-- This schema supports invoices, quotations, purchase orders, payment vouchers, and GRV

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE document_status AS ENUM ('draft', 'pending', 'approved', 'rejected', 'completed', 'cancelled');
CREATE TYPE payment_status AS ENUM ('pending', 'partial', 'paid', 'overdue', 'cancelled');
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'accountant', 'sales', 'procurement', 'viewer');

-- Companies table (for multi-tenant support)
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    registration_number VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    logo_url TEXT,
    currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table with role-based access
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'viewer',
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customers table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    tax_number VARCHAR(100),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    payment_terms INTEGER DEFAULT 30, -- days
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Suppliers table
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    tax_number VARCHAR(100),
    payment_terms INTEGER DEFAULT 30, -- days
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    code VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    unit_of_measure VARCHAR(50) NOT NULL,
    cost_price DECIMAL(15,2),
    selling_price DECIMAL(15,2),
    stock_quantity DECIMAL(15,3) DEFAULT 0,
    minimum_stock DECIMAL(15,3) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, code)
);

-- Quotations table
CREATE TABLE quotations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    quotation_number VARCHAR(100) NOT NULL,
    customer_id UUID REFERENCES customers(id),
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    customer_address TEXT,
    status document_status DEFAULT 'draft',
    quotation_date DATE NOT NULL,
    valid_until DATE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    notes TEXT,
    terms_conditions TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, quotation_number)
);

-- Invoices table
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    invoice_number VARCHAR(100) NOT NULL,
    quotation_id UUID REFERENCES quotations(id),
    customer_id UUID REFERENCES customers(id),
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    customer_address TEXT,
    status document_status DEFAULT 'draft',
    payment_status payment_status DEFAULT 'pending',
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) DEFAULT 0,
    notes TEXT,
    terms_conditions TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, invoice_number)
);

-- Purchase Orders table
CREATE TABLE purchase_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    po_number VARCHAR(100) NOT NULL,
    supplier_id UUID REFERENCES suppliers(id),
    supplier_name VARCHAR(255) NOT NULL,
    supplier_email VARCHAR(255),
    supplier_phone VARCHAR(50),
    supplier_address TEXT,
    status document_status DEFAULT 'draft',
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    notes TEXT,
    terms_conditions TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, po_number)
);

-- Payment Vouchers table
CREATE TABLE payment_vouchers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    voucher_number VARCHAR(100) NOT NULL,
    supplier_id UUID REFERENCES suppliers(id),
    purchase_order_id UUID REFERENCES purchase_orders(id),
    supplier_name VARCHAR(255) NOT NULL,
    payment_method VARCHAR(100) NOT NULL,
    reference_number VARCHAR(255),
    status document_status DEFAULT 'draft',
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    notes TEXT,
    created_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, voucher_number)
);

-- Goods Received Vouchers (GRV) table
CREATE TABLE goods_received_vouchers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    grv_number VARCHAR(100) NOT NULL,
    purchase_order_id UUID REFERENCES purchase_orders(id),
    supplier_id UUID REFERENCES suppliers(id),
    supplier_name VARCHAR(255) NOT NULL,
    delivery_note_number VARCHAR(255),
    status document_status DEFAULT 'draft',
    received_date DATE NOT NULL,
    received_by VARCHAR(255) NOT NULL,
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, grv_number)
);

-- Document Items tables (for line items in each document type)

-- Quotation Items
CREATE TABLE quotation_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quotation_id UUID REFERENCES quotations(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    product_code VARCHAR(100),
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invoice Items
CREATE TABLE invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    product_code VARCHAR(100),
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Purchase Order Items
CREATE TABLE purchase_order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    purchase_order_id UUID REFERENCES purchase_orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    product_code VARCHAR(100),
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    line_total DECIMAL(15,2) NOT NULL,
    received_quantity DECIMAL(15,3) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- GRV Items
CREATE TABLE grv_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    grv_id UUID REFERENCES goods_received_vouchers(id) ON DELETE CASCADE,
    purchase_order_item_id UUID REFERENCES purchase_order_items(id),
    product_id UUID REFERENCES products(id),
    product_code VARCHAR(100),
    product_name VARCHAR(255) NOT NULL,
    ordered_quantity DECIMAL(15,3) NOT NULL,
    received_quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2),
    condition VARCHAR(100) DEFAULT 'good', -- good, damaged, partial
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Document Sequences for auto-numbering
CREATE TABLE document_sequences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL, -- quotation, invoice, purchase_order, payment_voucher, grv
    prefix VARCHAR(20) DEFAULT '',
    current_number INTEGER DEFAULT 1,
    padding INTEGER DEFAULT 4, -- number of digits (e.g., 4 = 0001)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, document_type)
);

-- Audit Log table for tracking changes
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by UUID REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_customers_company_id ON customers(company_id);
CREATE INDEX idx_suppliers_company_id ON suppliers(company_id);
CREATE INDEX idx_products_company_id ON products(company_id);
CREATE INDEX idx_products_code ON products(company_id, code);
CREATE INDEX idx_quotations_company_id ON quotations(company_id);
CREATE INDEX idx_quotations_number ON quotations(company_id, quotation_number);
CREATE INDEX idx_quotations_customer ON quotations(customer_id);
CREATE INDEX idx_quotations_date ON quotations(quotation_date);
CREATE INDEX idx_invoices_company_id ON invoices(company_id);
CREATE INDEX idx_invoices_number ON invoices(company_id, invoice_number);
CREATE INDEX idx_invoices_customer ON invoices(customer_id);
CREATE INDEX idx_invoices_date ON invoices(invoice_date);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_payment_status ON invoices(payment_status);
CREATE INDEX idx_purchase_orders_company_id ON purchase_orders(company_id);
CREATE INDEX idx_purchase_orders_number ON purchase_orders(company_id, po_number);
CREATE INDEX idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX idx_payment_vouchers_company_id ON payment_vouchers(company_id);
CREATE INDEX idx_payment_vouchers_supplier ON payment_vouchers(supplier_id);
CREATE INDEX idx_grv_company_id ON goods_received_vouchers(company_id);
CREATE INDEX idx_grv_po ON goods_received_vouchers(purchase_order_id);
CREATE INDEX idx_audit_logs_company_id ON audit_logs(company_id);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);

-- Enable Row Level Security (RLS)
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotations ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotation_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_vouchers ENABLE ROW LEVEL SECURITY;
ALTER TABLE goods_received_vouchers ENABLE ROW LEVEL SECURITY;
ALTER TABLE grv_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_sequences ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's company_id
CREATE OR REPLACE FUNCTION get_user_company_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT company_id
        FROM users
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has role
CREATE OR REPLACE FUNCTION user_has_role(required_role user_role)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role = required_role OR role = 'admin'
        FROM users
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for companies table
CREATE POLICY "Users can view their own company" ON companies
    FOR SELECT USING (id = get_user_company_id());

CREATE POLICY "Admins can update their company" ON companies
    FOR UPDATE USING (id = get_user_company_id() AND user_has_role('admin'));

-- RLS Policies for users table
CREATE POLICY "Users can view users in their company" ON users
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Admins can manage users in their company" ON users
    FOR ALL USING (company_id = get_user_company_id() AND user_has_role('admin'));

-- RLS Policies for customers table
CREATE POLICY "Users can view customers in their company" ON customers
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Sales and managers can manage customers" ON customers
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('sales'))
    );

-- RLS Policies for suppliers table
CREATE POLICY "Users can view suppliers in their company" ON suppliers
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Procurement and managers can manage suppliers" ON suppliers
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('procurement'))
    );

-- RLS Policies for products table
CREATE POLICY "Users can view products in their company" ON products
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Managers can manage products" ON products
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager'))
    );

-- RLS Policies for quotations table
CREATE POLICY "Users can view quotations in their company" ON quotations
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Sales can manage quotations" ON quotations
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('sales'))
    );

-- RLS Policies for invoices table
CREATE POLICY "Users can view invoices in their company" ON invoices
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Sales and accountants can manage invoices" ON invoices
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('sales') OR user_has_role('accountant'))
    );

-- RLS Policies for purchase orders table
CREATE POLICY "Users can view purchase orders in their company" ON purchase_orders
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Procurement can manage purchase orders" ON purchase_orders
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('procurement'))
    );

-- RLS Policies for payment vouchers table
CREATE POLICY "Users can view payment vouchers in their company" ON payment_vouchers
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Accountants can manage payment vouchers" ON payment_vouchers
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('accountant'))
    );

-- RLS Policies for GRV table
CREATE POLICY "Users can view GRVs in their company" ON goods_received_vouchers
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Procurement can manage GRVs" ON goods_received_vouchers
    FOR ALL USING (
        company_id = get_user_company_id() AND
        (user_has_role('admin') OR user_has_role('manager') OR user_has_role('procurement'))
    );

-- RLS Policies for document items (inherit from parent document)
CREATE POLICY "Users can view quotation items" ON quotation_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM quotations q
            WHERE q.id = quotation_items.quotation_id
            AND q.company_id = get_user_company_id()
        )
    );

CREATE POLICY "Sales can manage quotation items" ON quotation_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM quotations q
            WHERE q.id = quotation_items.quotation_id
            AND q.company_id = get_user_company_id()
        ) AND (user_has_role('admin') OR user_has_role('manager') OR user_has_role('sales'))
    );

-- Similar policies for other item tables...
CREATE POLICY "Users can view invoice items" ON invoice_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM invoices i
            WHERE i.id = invoice_items.invoice_id
            AND i.company_id = get_user_company_id()
        )
    );

CREATE POLICY "Sales and accountants can manage invoice items" ON invoice_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM invoices i
            WHERE i.id = invoice_items.invoice_id
            AND i.company_id = get_user_company_id()
        ) AND (user_has_role('admin') OR user_has_role('manager') OR user_has_role('sales') OR user_has_role('accountant'))
    );

-- RLS Policies for document sequences
CREATE POLICY "Users can view document sequences in their company" ON document_sequences
    FOR SELECT USING (company_id = get_user_company_id());

CREATE POLICY "Admins can manage document sequences" ON document_sequences
    FOR ALL USING (company_id = get_user_company_id() AND user_has_role('admin'));

-- RLS Policies for audit logs
CREATE POLICY "Users can view audit logs in their company" ON audit_logs
    FOR SELECT USING (company_id = get_user_company_id());

-- Utility functions for document numbering
CREATE OR REPLACE FUNCTION get_next_document_number(
    p_company_id UUID,
    p_document_type VARCHAR(50)
) RETURNS VARCHAR(100) AS $$
DECLARE
    v_sequence RECORD;
    v_next_number INTEGER;
    v_formatted_number VARCHAR(100);
BEGIN
    -- Get or create sequence record
    SELECT * INTO v_sequence
    FROM document_sequences
    WHERE company_id = p_company_id AND document_type = p_document_type;

    IF NOT FOUND THEN
        INSERT INTO document_sequences (company_id, document_type, current_number)
        VALUES (p_company_id, p_document_type, 1)
        RETURNING * INTO v_sequence;
    END IF;

    v_next_number := v_sequence.current_number;

    -- Update the sequence
    UPDATE document_sequences
    SET current_number = current_number + 1,
        updated_at = NOW()
    WHERE id = v_sequence.id;

    -- Format the number with padding
    v_formatted_number := v_sequence.prefix || LPAD(v_next_number::TEXT, v_sequence.padding, '0');

    RETURN v_formatted_number;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate document totals
CREATE OR REPLACE FUNCTION calculate_document_totals(
    p_subtotal DECIMAL(15,2),
    p_tax_rate DECIMAL(5,2) DEFAULT 0,
    p_discount_amount DECIMAL(15,2) DEFAULT 0
) RETURNS TABLE(
    tax_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY SELECT
        ROUND((p_subtotal - COALESCE(p_discount_amount, 0)) * COALESCE(p_tax_rate, 0) / 100, 2) AS tax_amount,
        ROUND(p_subtotal - COALESCE(p_discount_amount, 0) +
              (p_subtotal - COALESCE(p_discount_amount, 0)) * COALESCE(p_tax_rate, 0) / 100, 2) AS total_amount;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to update document totals
CREATE OR REPLACE FUNCTION update_document_totals() RETURNS TRIGGER AS $$
DECLARE
    v_subtotal DECIMAL(15,2);
    v_totals RECORD;
BEGIN
    -- Calculate subtotal based on document type
    IF TG_TABLE_NAME = 'quotation_items' THEN
        SELECT COALESCE(SUM(line_total), 0) INTO v_subtotal
        FROM quotation_items
        WHERE quotation_id = COALESCE(NEW.quotation_id, OLD.quotation_id);

        SELECT * INTO v_totals FROM calculate_document_totals(
            v_subtotal,
            (SELECT tax_rate FROM quotations WHERE id = COALESCE(NEW.quotation_id, OLD.quotation_id)),
            (SELECT discount_amount FROM quotations WHERE id = COALESCE(NEW.quotation_id, OLD.quotation_id))
        );

        UPDATE quotations
        SET subtotal = v_subtotal,
            tax_amount = v_totals.tax_amount,
            total_amount = v_totals.total_amount,
            updated_at = NOW()
        WHERE id = COALESCE(NEW.quotation_id, OLD.quotation_id);

    ELSIF TG_TABLE_NAME = 'invoice_items' THEN
        SELECT COALESCE(SUM(line_total), 0) INTO v_subtotal
        FROM invoice_items
        WHERE invoice_id = COALESCE(NEW.invoice_id, OLD.invoice_id);

        SELECT * INTO v_totals FROM calculate_document_totals(
            v_subtotal,
            (SELECT tax_rate FROM invoices WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id)),
            (SELECT discount_amount FROM invoices WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id))
        );

        UPDATE invoices
        SET subtotal = v_subtotal,
            tax_amount = v_totals.tax_amount,
            total_amount = v_totals.total_amount,
            balance_due = v_totals.total_amount - paid_amount,
            updated_at = NOW()
        WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id);

    ELSIF TG_TABLE_NAME = 'purchase_order_items' THEN
        SELECT COALESCE(SUM(line_total), 0) INTO v_subtotal
        FROM purchase_order_items
        WHERE purchase_order_id = COALESCE(NEW.purchase_order_id, OLD.purchase_order_id);

        SELECT * INTO v_totals FROM calculate_document_totals(
            v_subtotal,
            (SELECT tax_rate FROM purchase_orders WHERE id = COALESCE(NEW.purchase_order_id, OLD.purchase_order_id)),
            0
        );

        UPDATE purchase_orders
        SET subtotal = v_subtotal,
            tax_amount = v_totals.tax_amount,
            total_amount = v_totals.total_amount,
            updated_at = NOW()
        WHERE id = COALESCE(NEW.purchase_order_id, OLD.purchase_order_id);
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic total calculation
CREATE TRIGGER trigger_update_quotation_totals
    AFTER INSERT OR UPDATE OR DELETE ON quotation_items
    FOR EACH ROW EXECUTE FUNCTION update_document_totals();

CREATE TRIGGER trigger_update_invoice_totals
    AFTER INSERT OR UPDATE OR DELETE ON invoice_items
    FOR EACH ROW EXECUTE FUNCTION update_document_totals();

CREATE TRIGGER trigger_update_po_totals
    AFTER INSERT OR UPDATE OR DELETE ON purchase_order_items
    FOR EACH ROW EXECUTE FUNCTION update_document_totals();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updating timestamps
CREATE TRIGGER trigger_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_quotations_updated_at BEFORE UPDATE ON quotations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_invoices_updated_at BEFORE UPDATE ON invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_purchase_orders_updated_at BEFORE UPDATE ON purchase_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_payment_vouchers_updated_at BEFORE UPDATE ON payment_vouchers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_grv_updated_at BEFORE UPDATE ON goods_received_vouchers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
