import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string | string[];
  requiredPermission?: string;
  fallback?: ReactNode;
}

export default function ProtectedRoute({
  children,
  requiredRole,
  requiredPermission,
  fallback,
}: ProtectedRouteProps) {
  const { user, userProfile, loading, hasRole, hasPermission } = useAuth();
  const location = useLocation();

  // Check if we're in demo mode
  const isDemoMode = !import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY === 'your-anon-key-here';

  // Show loading spinner while checking authentication (but not in demo mode)
  if (loading && !isDemoMode) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // In demo mode, allow access to dashboard
  if (isDemoMode && location.pathname === '/dashboard') {
    return <>{children}</>;
  }

  // Redirect to login if not authenticated (and not in demo mode)
  if (!isDemoMode && (!user || !userProfile)) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Skip role and permission checks in demo mode
  if (!isDemoMode) {
    // Check role requirements
    if (requiredRole && !hasRole(requiredRole)) {
      return (
        fallback || (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-destructive mb-2">Access Denied</h2>
              <p className="text-muted-foreground">
                You don't have permission to access this page.
              </p>
            </div>
          </div>
        )
      );
    }

    // Check permission requirements
    if (requiredPermission && !hasPermission(requiredPermission)) {
      return (
        fallback || (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-destructive mb-2">Access Denied</h2>
              <p className="text-muted-foreground">
                You don't have permission to access this page.
              </p>
            </div>
          </div>
        )
      );
    }
  }

  return <>{children}</>;
}
