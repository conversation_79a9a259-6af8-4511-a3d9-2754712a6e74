import SEO from "@/components/SEO";
import { Shield<PERSON>heck, FileCheck2, ClipboardSignature } from "lucide-react";

export default function Quality() {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: '<PERSON><PERSON><PERSON>',
    url: '/',
    logo: '/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png',
  };

  return (
    <main className="container py-12">
      <SEO
        title="Quality & Compliance | Manechem"
        description="ISO 9001 QMS, Responsible Care, audited supply base, full batch traceability, and regulatory support."
        canonical="/quality"
        jsonLd={jsonLd}
      />
      <h1 className="mb-6 text-4xl font-bold">Quality & Compliance</h1>
      <div className="grid gap-6 md:grid-cols-3">
        <article className="rounded-lg border p-6">
          <h2 className="flex items-center gap-2 text-lg font-semibold"><ShieldCheck className="text-primary" /> ISO 9001 QMS</h2>
          <p className="mt-2 text-sm text-muted-foreground">Documented procedures, CAPA, and management review ensure consistent outcomes.</p>
        </article>
        <article className="rounded-lg border p-6">
          <h2 className="flex items-center gap-2 text-lg font-semibold"><FileCheck2 className="text-primary" /> Traceability</h2>
          <p className="mt-2 text-sm text-muted-foreground">Batch-level tracking from producer to point-of-use with COA and SDS linkage.</p>
        </article>
        <article className="rounded-lg border p-6">
          <h2 className="flex items-center gap-2 text-lg font-semibold"><ClipboardSignature className="text-primary" /> Regulatory</h2>
          <p className="mt-2 text-sm text-muted-foreground">REACH, TSCA, GHS/CLP, and site-specific labeling and documentation support.</p>
        </article>
      </div>
    </main>
  );
}
