import SEO from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FlaskConical, Beaker, Droplets, Atom, TestTubeDiagonal } from "lucide-react";

const categories = [
  { icon: FlaskConical, title: "Solvents", desc: "Acetone, IPA, MEK, Toluene, Ethanol and more." },
  { icon: Droplets, title: "Acids & Bases", desc: "Sulfuric, Hydrochloric, Nitric, Sodium Hydroxide, Ammonia." },
  { icon: Beaker, title: "Glycols & Polymers", desc: "MEG, MPG, PEGs, resins, and specialty polymers." },
  { icon: Atom, title: "Intermediates", desc: "Fine chemicals and intermediates for synthesis and scale-up." },
  { icon: TestTubeDiagonal, title: "Food & Pharma", desc: "USP/EP grade excipients and food-grade additives." },
];

export default function Products() {
  return (
    <main className="container py-12">
      <SEO
        title="Products | Manechem"
        description="Explore our portfolio of solvents, acids, bases, glycols, intermediates, and pharma-grade ingredients."
        canonical="/products"
      />
      <h1 className="mb-6 text-4xl font-bold">Chemical Products</h1>
      <p className="mb-10 max-w-2xl text-muted-foreground">
        Sourced from audited global producers and distributed with full batch traceability. SDS and COA available for every shipment.
      </p>
      <section className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {categories.map(({ icon: Icon, title, desc }) => (
          <Card key={title} className="transition hover:shadow-elevated">
            <CardHeader>
              <CardTitle className="flex items-center gap-3"><Icon className="text-primary" />{title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{desc}</p>
            </CardContent>
          </Card>
        ))}
      </section>
    </main>
  );
}
