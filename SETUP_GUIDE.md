# Manechem Backend Setup Guide

## Quick Setup Instructions

### Step 1: Create Supabase Project

1. **Go to Supabase Dashboard**:
   - Visit [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Sign in or create an account

2. **Create New Project**:
   - Click "New Project"
   - Choose your organization
   - Enter project name: "<PERSON><PERSON><PERSON>"
   - Set a strong database password
   - Choose a region close to you
   - Click "Create new project"

3. **Wait for Setup**:
   - Project creation takes 1-2 minutes
   - You'll see a progress indicator

### Step 2: Get Project Credentials

1. **Go to Project Settings**:
   - Click the gear icon (⚙️) in the left sidebar
   - Select "API" from the settings menu

2. **Copy Your Credentials**:
   - **Project URL**: Copy the URL (looks like `https://xxxxx.supabase.co`)
   - **Anon Key**: Copy the `anon` `public` key (long string starting with `eyJ...`)

### Step 3: Configure Environment Variables

1. **Update .env file**:
   ```bash
   # Replace the placeholder values in .env with your actual credentials
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key-here
   ```

### Step 4: Set Up Database Schema

1. **Go to SQL Editor**:
   - In your Supabase dashboard, click "SQL Editor" in the left sidebar

2. **Run Database Schema**:
   - Copy the entire contents of `database/schema.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute the script
   - This will create all tables, functions, and security policies

### Step 5: Test the Application

1. **Restart Development Server**:
   ```bash
   # Stop the current server (Ctrl+C)
   # Then restart it
   npm run dev
   ```

2. **Check for Errors**:
   - Open browser console (F12)
   - Look for any Supabase connection errors
   - You should see the app loading without errors

### Step 6: Create Your First User

1. **Go to Authentication Page**:
   - Visit `http://localhost:8080/auth`
   - Click "Sign up"

2. **Create Account**:
   - Enter your email and password
   - Fill in your full name
   - Click "Create Account"

3. **Check Email**:
   - Check your email for confirmation link
   - Click the link to verify your account

### Step 7: Set Up Company and User Profile

After confirming your email, you'll need to manually set up your company and user profile in the database:

1. **Go to Supabase Table Editor**:
   - In Supabase dashboard, click "Table Editor"

2. **Create Company Record**:
   - Go to `companies` table
   - Click "Insert" → "Insert row"
   - Fill in:
     - `name`: "Manechem"
     - `currency`: "USD" (or your preferred currency)
     - `email`: "<EMAIL>"
     - `phone`: "+263 *********"
   - Click "Save"
   - Note the company ID (UUID)

3. **Update User Record**:
   - Go to `users` table
   - Find your user record (should have your email)
   - Click "Edit"
   - Set:
     - `company_id`: The company ID from step 2
     - `role`: "admin"
     - `full_name`: Your name
   - Click "Save"

### Step 8: Access Dashboard

1. **Sign In**:
   - Go to `http://localhost:8080/auth`
   - Sign in with your credentials

2. **Access Dashboard**:
   - You should be redirected to `http://localhost:8080/dashboard`
   - You should see the Manechem dashboard with your company info

## Troubleshooting

### Common Issues:

1. **"Missing Supabase environment variables" Error**:
   - Make sure `.env` file exists in project root
   - Check that environment variables are set correctly
   - Restart the development server after changing `.env`

2. **"Invalid API key" Error**:
   - Double-check your anon key from Supabase dashboard
   - Make sure you copied the full key (starts with `eyJ...`)

3. **Database Connection Issues**:
   - Verify your project URL is correct
   - Check that your Supabase project is active
   - Try refreshing the Supabase dashboard

4. **Authentication Not Working**:
   - Check email confirmation
   - Verify user exists in `users` table
   - Make sure `company_id` is set for the user

5. **Permission Denied Errors**:
   - Ensure user has correct role set
   - Check that company_id matches between user and company
   - Verify RLS policies are working

### Getting Help:

If you encounter issues:
1. Check browser console for error messages
2. Check Supabase logs in the dashboard
3. Verify database schema was created correctly
4. Make sure all environment variables are set

## Next Steps

Once setup is complete:
1. ✅ Authentication working
2. ✅ Dashboard accessible
3. 🔄 Ready to build document management UI
4. 🔄 Ready to add PDF generation
5. 🔄 Ready to create reports and analytics

The backend system is now fully functional and ready for the frontend UI components!
