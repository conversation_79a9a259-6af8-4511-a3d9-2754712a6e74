import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-warehouse.jpg";
import { Link } from "react-router-dom";

interface HeroProps {
  title: string;
  subtitle: string;
}

export default function Hero({ title, subtitle }: HeroProps) {
  useEffect(() => {
    const root = document.documentElement;
    const onMove = (e: MouseEvent) => {
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) return;
      const x = (e.clientX / window.innerWidth).toFixed(2);
      const y = (e.clientY / window.innerHeight).toFixed(2);
      root.style.setProperty('--pointer-x', x);
      root.style.setProperty('--pointer-y', y);
    };
    window.addEventListener('mousemove', onMove);
    return () => window.removeEventListener('mousemove', onMove);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-accent/5">
      <div className="absolute inset-0 -z-10 bg-surface-gradient opacity-60" aria-hidden />
      {/* Animated background elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse" aria-hidden />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/20 rounded-full blur-3xl animate-pulse delay-1000" aria-hidden />

      <div className="container grid gap-8 py-16 md:grid-cols-2 md:py-20 lg:py-28 relative z-10">
        <div className="flex flex-col justify-center gap-6">
          <div className="inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full w-fit">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            ISO 9001 Certified • REACH Compliant
          </div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
            {title}
          </h1>
          <p className="text-lg text-muted-foreground max-w-prose leading-relaxed">
            {subtitle}
          </p>
          <div className="flex flex-wrap gap-3">
            <Link to="/contact">
              <Button variant="hero" size="lg" className="shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Request a Quote
              </Button>
            </Link>
            <Link to="/products">
              <Button variant="outlinePrimary" size="lg" className="hover:shadow-md transition-all duration-300">
                Explore Products
              </Button>
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="flex items-center gap-6 pt-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>24/7 Support</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Global Network</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span>Same-day SDS</span>
            </div>
          </div>
        </div>
        <div className="relative">
          <img
            src={heroImage}
            alt="Modern chemical distribution warehouse with drums and IBC totes"
            className="w-full rounded-lg border shadow-elevated animate-fade-up hover:shadow-2xl transition-shadow duration-500"
            loading="eager"
          />
          <div className="absolute -bottom-6 left-6 right-6 h-24 bg-gradient-primary opacity-30 blur-2xl rounded-full pointer-events-none" aria-hidden />

          {/* Floating stats cards */}
          <div className="absolute -top-4 -left-4 bg-background/90 backdrop-blur-sm border rounded-lg p-3 shadow-lg">
            <div className="text-2xl font-bold text-primary">500+</div>
            <div className="text-xs text-muted-foreground">Products</div>
          </div>
          <div className="absolute -bottom-4 -right-4 bg-background/90 backdrop-blur-sm border rounded-lg p-3 shadow-lg">
            <div className="text-2xl font-bold text-primary">99.9%</div>
            <div className="text-xs text-muted-foreground">On-time</div>
          </div>
        </div>
      </div>
    </section>
  );
}
