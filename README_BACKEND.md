# Manechem Backend System

## Overview

This is a comprehensive business management system for Manechem that includes:

- **Invoices** - Create, manage, and track customer invoices
- **Quotations** - Generate quotations for potential customers
- **Purchase Orders** - Manage supplier purchase orders
- **Payment Vouchers** - Process supplier payments
- **Goods Received Vouchers (GRV)** - Track received goods from suppliers

## Architecture

### Database Layer (Supabase)
- PostgreSQL database with Row Level Security (RLS)
- Multi-tenant architecture with company-based data isolation
- Automated document numbering and total calculations
- Audit logging for all changes

### API Layer
- Type-safe Supabase client with TypeScript
- Service classes for each business entity
- React Query hooks for data fetching and caching
- Optimistic updates and error handling

### Authentication & Authorization
- Supabase Auth with email/password
- Role-based access control (Admin, Manager, Accountant, Sales, Procurement, Viewer)
- Permission-based UI rendering
- Protected routes and components

## Setup Instructions

### 1. Database Setup

1. **Create Supabase Project**:
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Create a new project or use existing one
   - Note your project URL and anon key

2. **Run Database Schema**:
   - Go to SQL Editor in Supabase Dashboard
   - Copy and paste the contents of `database/schema.sql`
   - Execute the script to create all tables, functions, and policies

3. **Configure Environment Variables**:
   ```bash
   cp .env.example .env
   ```
   
   Update `.env` with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   ```

### 2. Application Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start Development Server**:
   ```bash
   npm run dev
   ```

3. **Access the Application**:
   - Public site: http://localhost:8080/
   - Authentication: http://localhost:8080/auth
   - Dashboard: http://localhost:8080/dashboard (requires login)

## Database Schema

### Core Tables

- **companies** - Multi-tenant company data
- **users** - User accounts with role-based access
- **customers** - Customer management
- **suppliers** - Supplier management
- **products** - Product catalog with inventory

### Document Tables

- **quotations** - Customer quotations
- **invoices** - Customer invoices with payment tracking
- **purchase_orders** - Supplier purchase orders
- **payment_vouchers** - Supplier payment processing
- **goods_received_vouchers** - Goods receipt tracking

### Item Tables

- **quotation_items** - Line items for quotations
- **invoice_items** - Line items for invoices
- **purchase_order_items** - Line items for purchase orders
- **grv_items** - Line items for goods received vouchers

### System Tables

- **document_sequences** - Auto-numbering for documents
- **audit_logs** - Change tracking and audit trail

## User Roles & Permissions

### Admin
- Full system access
- User management
- Company settings
- All document types

### Manager
- Dashboard access
- Customer/supplier management
- Product management
- All document types
- Reports

### Sales
- Customer management
- Quotations and invoices
- Product viewing
- Dashboard access

### Accountant
- Invoice management
- Payment vouchers
- Financial reports
- Customer viewing

### Procurement
- Supplier management
- Purchase orders
- Goods received vouchers
- Product viewing

### Viewer
- Read-only access to all documents
- Dashboard viewing
- No create/edit permissions

## Features

### Document Management
- Auto-numbering with customizable prefixes
- Line item management with automatic total calculation
- Status tracking (Draft, Pending, Approved, etc.)
- Document relationships (Quote → Invoice, PO → GRV)

### Financial Tracking
- Invoice payment status tracking
- Partial payment support
- Overdue invoice identification
- Payment voucher approval workflow

### Inventory Management
- Stock quantity tracking
- Automatic stock updates from GRV
- Low stock alerts
- Product cost and selling price management

### Security
- Row Level Security (RLS) policies
- Company data isolation
- Role-based access control
- Audit logging

### API Features
- Type-safe database operations
- Optimistic updates
- Error handling with user feedback
- Real-time data synchronization

## Next Steps

1. **Complete UI Implementation** - Build forms and views for all document types
2. **PDF Generation** - Add PDF export for invoices, quotations, etc.
3. **Reporting Dashboard** - Create analytics and business reports
4. **Email Integration** - Send documents via email
5. **Mobile Responsiveness** - Optimize for mobile devices
6. **Advanced Features** - Recurring invoices, multi-currency support, etc.

## Development Guidelines

### Adding New Features
1. Update database schema if needed
2. Add/update service methods
3. Create React Query hooks
4. Build UI components
5. Add proper error handling
6. Update permissions if needed

### Testing
- Test all CRUD operations
- Verify RLS policies work correctly
- Test role-based access
- Validate document calculations
- Check audit logging

### Security Considerations
- Never expose service keys in client code
- Always use RLS policies
- Validate user permissions
- Sanitize user inputs
- Use prepared statements for queries
