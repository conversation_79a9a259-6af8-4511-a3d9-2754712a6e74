import SEO from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Truck, ClipboardList, ShieldCheck, Boxes, Ship } from "lucide-react";

const services = [
  { icon: Truck, title: 'Distribution & Last-Mile', desc: 'Dedicated fleet with ADR/HAZMAT compliance and GPS tracking.' },
  { icon: ClipboardList, title: 'Vendor-Managed Inventory', desc: 'Consignment, JIT, and call-off programs to reduce working capital.' },
  { icon: ShieldCheck, title: 'Regulatory & QA', desc: 'REACH, TSCA, GHS labeling, batch traceability, and change control.' },
  { icon: Boxes, title: 'Packaging & Blending', desc: 'Returnable totes, drums, IBCs, custom dilutions, and toll blending.' },
  { icon: Ship, title: 'Global Sourcing', desc: 'Audited producers, import logistics, and multimodal transport.' },
];

export default function Services() {
  return (
    <main className="container py-12">
      <SEO
        title="Services | Manechem"
        description="Distribution, inventory programs, regulatory support, packaging, blending, and global sourcing."
        canonical="/services"
      />
      <h1 className="mb-6 text-4xl font-bold">Services</h1>
      <p className="mb-10 max-w-2xl text-muted-foreground">Reliable execution backed by compliance and data visibility at every step.</p>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {services.map(({ icon: Icon, title, desc }) => (
          <Card key={title} className="transition hover:shadow-elevated">
            <CardHeader>
              <CardTitle className="flex items-center gap-3"><Icon className="text-primary" />{title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{desc}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </main>
  );
}
