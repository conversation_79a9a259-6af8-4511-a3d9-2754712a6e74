import { useState } from "react";
import SEO from "@/components/SEO";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Phone, Mail, MessageCircle } from "lucide-react";

export default function Contact() {
  const [loading, setLoading] = useState(false);

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast({ title: 'Message sent', description: 'We\'ll get back to you within 1 business day.' });
    }, 800);
  };

  const handleWhatsApp = () => {
    const phoneNumber = "263779023063"; // Remove + and spaces for WhatsApp URL
    const message = "Hello! I'm interested in learning more about Manechem's chemical distribution services.";
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <main className="container py-12">
      <SEO
        title="Contact | Manechem"
        description="Request a quote or speak with our team about products, supply programs, and regulatory support."
        canonical="/contact"
      />
      <h1 className="mb-6 text-4xl font-bold">Contact</h1>
      <div className="grid gap-10 md:grid-cols-2">
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            <Input required name="name" placeholder="Full name" />
            <Input required type="email" name="email" placeholder="Work email" />
          </div>
          <Input name="company" placeholder="Company" />
          <Input name="phone" placeholder="Phone" />
          <Textarea required name="message" placeholder="How can we help?" rows={6} />
          <div className="flex flex-col sm:flex-row gap-3">
            <Button type="submit" variant="hero" disabled={loading} className="flex-1">
              {loading ? 'Sending…' : 'Send message'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleWhatsApp}
              className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300"
            >
              <MessageCircle className="h-4 w-4" />
              WhatsApp
            </Button>
          </div>
        </form>
        <aside className="space-y-6">
          <p className="text-muted-foreground">Prefer speaking to someone? Our specialists can help specify materials, packaging, and delivery cadence.</p>
          <div className="space-y-3 text-sm">
            <p className="flex items-center gap-2"><Phone className="text-primary" /> +263 779023063 | +263 726170836</p>
            <p className="flex items-center gap-2"><Mail className="text-primary" /> <EMAIL></p>
            <p className="flex items-center gap-2"><Mail className="text-primary" /> <EMAIL></p>
            <button
              onClick={handleWhatsApp}
              className="flex items-center gap-2 text-green-600 hover:text-green-700 transition-colors cursor-pointer"
            >
              <MessageCircle className="text-green-600" />
              WhatsApp: +263 779023063
            </button>
          </div>
        </aside>
      </div>
    </main>
  );
}
