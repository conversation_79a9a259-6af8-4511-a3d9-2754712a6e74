import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase, Tables } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  userProfile: Tables<'users'> | null;
  company: Tables<'companies'> | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<Tables<'users'>>) => Promise<{ error: Error | null }>;
  hasRole: (role: string | string[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<Tables<'users'> | null>(null);
  const [company, setCompany] = useState<Tables<'companies'> | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        await loadUserProfile(session.user.id);
      } else {
        setUserProfile(null);
        setCompany(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      // Load user profile
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error loading user profile:', profileError);
        setLoading(false);
        return;
      }

      setUserProfile(profile);

      // Load company data
      if (profile?.company_id) {
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('*')
          .eq('id', profile.company_id)
          .single();

        if (companyError) {
          console.error('Error loading company:', companyError);
        } else {
          setCompany(companyData);
        }
      }
    } catch (error) {
      console.error('Error in loadUserProfile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);

    // Check if Supabase is properly configured
    if (!import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY === 'your-anon-key-here') {
      const error = new Error('Database not configured. Please set up your Supabase project first.');
      toast({
        title: 'Database Not Connected',
        description: 'Please configure your Supabase database to use authentication. Check the setup guide.',
        variant: 'destructive',
      });
      setLoading(false);
      return { error };
    }

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: 'Sign In Failed',
          description: error.message,
          variant: 'destructive',
        });
      }

      setLoading(false);
      return { error };
    } catch (error) {
      const authError = error as Error;
      toast({
        title: 'Connection Error',
        description: 'Unable to connect to authentication service. Please check your setup.',
        variant: 'destructive',
      });
      setLoading(false);
      return { error: authError };
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    setLoading(true);

    // Check if Supabase is properly configured
    if (!import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY === 'your-anon-key-here') {
      const error = new Error('Database not configured. Please set up your Supabase project first.');
      toast({
        title: 'Database Not Connected',
        description: 'Please configure your Supabase database to use authentication. Check the setup guide.',
        variant: 'destructive',
      });
      setLoading(false);
      return { error };
    }

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        toast({
          title: 'Sign Up Failed',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Check your email',
          description: 'We sent you a confirmation link.',
        });
      }

      setLoading(false);
      return { error };
    } catch (error) {
      const authError = error as Error;
      toast({
        title: 'Connection Error',
        description: 'Unable to connect to authentication service. Please check your setup.',
        variant: 'destructive',
      });
      setLoading(false);
      return { error: authError };
    }
  };

  const signOut = async () => {
    setLoading(true);
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      toast({
        title: 'Sign Out Failed',
        description: error.message,
        variant: 'destructive',
      });
    }
    
    setUser(null);
    setUserProfile(null);
    setCompany(null);
    setSession(null);
    setLoading(false);
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      toast({
        title: 'Password Reset Failed',
        description: error.message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Check your email',
        description: 'We sent you a password reset link.',
      });
    }

    return { error };
  };

  const updateProfile = async (updates: Partial<Tables<'users'>>) => {
    if (!userProfile) {
      return { error: new Error('No user profile found') };
    }

    const { error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userProfile.id);

    if (error) {
      toast({
        title: 'Update Failed',
        description: error.message,
        variant: 'destructive',
      });
      return { error };
    }

    // Reload user profile
    await loadUserProfile(userProfile.id);
    
    toast({
      title: 'Profile Updated',
      description: 'Your profile has been updated successfully.',
    });

    return { error: null };
  };

  const hasRole = (role: string | string[]): boolean => {
    if (!userProfile) return false;
    
    const roles = Array.isArray(role) ? role : [role];
    return roles.includes(userProfile.role) || userProfile.role === 'admin';
  };

  const hasPermission = (permission: string): boolean => {
    if (!userProfile) return false;
    
    // Define role-based permissions
    const permissions: Record<string, string[]> = {
      admin: ['*'], // Admin has all permissions
      manager: [
        'view_dashboard',
        'manage_customers',
        'manage_suppliers',
        'manage_products',
        'create_quotations',
        'manage_quotations',
        'create_invoices',
        'manage_invoices',
        'create_purchase_orders',
        'manage_purchase_orders',
        'view_reports',
      ],
      sales: [
        'view_dashboard',
        'view_customers',
        'manage_customers',
        'view_products',
        'create_quotations',
        'manage_quotations',
        'create_invoices',
        'manage_invoices',
      ],
      accountant: [
        'view_dashboard',
        'view_customers',
        'view_suppliers',
        'view_invoices',
        'manage_invoices',
        'create_payment_vouchers',
        'manage_payment_vouchers',
        'view_reports',
      ],
      procurement: [
        'view_dashboard',
        'view_suppliers',
        'manage_suppliers',
        'view_products',
        'create_purchase_orders',
        'manage_purchase_orders',
        'create_grv',
        'manage_grv',
      ],
      viewer: [
        'view_dashboard',
        'view_customers',
        'view_suppliers',
        'view_products',
        'view_quotations',
        'view_invoices',
        'view_purchase_orders',
        'view_payment_vouchers',
        'view_grv',
      ],
    };

    const userPermissions = permissions[userProfile.role] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  };

  const value: AuthContextType = {
    user,
    userProfile,
    company,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    hasRole,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
