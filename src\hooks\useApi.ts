import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import {
  companyService,
  userService,
  customerService,
  supplierService,
  productService,
  quotationService,
  invoiceService,
  purchaseOrderService,
  paymentVoucherService,
  grvService,
} from '@/services/api';

// Query keys
export const queryKeys = {
  companies: ['companies'],
  users: ['users'],
  customers: ['customers'],
  suppliers: ['suppliers'],
  products: ['products'],
  quotations: ['quotations'],
  invoices: ['invoices'],
  purchaseOrders: ['purchase-orders'],
  paymentVouchers: ['payment-vouchers'],
  grvs: ['grvs'],
} as const;

// Company hooks
export const useCompany = () => {
  return useQuery({
    queryKey: queryKeys.companies,
    queryFn: () => companyService.getCurrentCompany(),
  });
};

// User hooks
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.users,
    queryFn: () => userService.getCurrentUser(),
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: userService.updateCurrentUser.bind(userService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users });
      toast({
        title: 'Success',
        description: 'User profile updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Customer hooks
export const useCustomers = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.customers, filters],
    queryFn: () => customerService.getAll(filters),
  });
};

export const useCustomer = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.customers, id],
    queryFn: () => customerService.getById(id),
    enabled: !!id,
  });
};

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: customerService.create.bind(customerService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.customers });
      toast({
        title: 'Success',
        description: 'Customer created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      customerService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.customers });
      toast({
        title: 'Success',
        description: 'Customer updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: customerService.delete.bind(customerService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.customers });
      toast({
        title: 'Success',
        description: 'Customer deleted successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Supplier hooks
export const useSuppliers = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.suppliers, filters],
    queryFn: () => supplierService.getAll(filters),
  });
};

export const useSupplier = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.suppliers, id],
    queryFn: () => supplierService.getById(id),
    enabled: !!id,
  });
};

export const useCreateSupplier = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: supplierService.create.bind(supplierService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.suppliers });
      toast({
        title: 'Success',
        description: 'Supplier created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Product hooks
export const useProducts = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.products, filters],
    queryFn: () => productService.getAll(filters),
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.products, id],
    queryFn: () => productService.getById(id),
    enabled: !!id,
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: productService.create.bind(productService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
      toast({
        title: 'Success',
        description: 'Product created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Quotation hooks
export const useQuotations = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.quotations, filters],
    queryFn: () => quotationService.getAll(filters),
  });
};

export const useQuotation = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.quotations, id],
    queryFn: () => quotationService.getWithItems(id),
    enabled: !!id,
  });
};

export const useCreateQuotation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ quotation, items }: { quotation: any; items: any[] }) =>
      quotationService.createWithItems(quotation, items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.quotations });
      toast({
        title: 'Success',
        description: 'Quotation created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Invoice hooks
export const useInvoices = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.invoices, filters],
    queryFn: () => invoiceService.getAll(filters),
  });
};

export const useInvoice = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.invoices, id],
    queryFn: () => invoiceService.getWithItems(id),
    enabled: !!id,
  });
};

export const useCreateInvoice = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ invoice, items }: { invoice: any; items: any[] }) =>
      invoiceService.createWithItems(invoice, items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.invoices });
      toast({
        title: 'Success',
        description: 'Invoice created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};

// Purchase Order hooks
export const usePurchaseOrders = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: [...queryKeys.purchaseOrders, filters],
    queryFn: () => purchaseOrderService.getAll(filters),
  });
};

export const usePurchaseOrder = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.purchaseOrders, id],
    queryFn: () => purchaseOrderService.getWithItems(id),
    enabled: !!id,
  });
};

export const useCreatePurchaseOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ purchaseOrder, items }: { purchaseOrder: any; items: any[] }) =>
      purchaseOrderService.createWithItems(purchaseOrder, items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.purchaseOrders });
      toast({
        title: 'Success',
        description: 'Purchase order created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};
