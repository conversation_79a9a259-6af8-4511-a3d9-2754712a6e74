import { AlertTriangle, ExternalLink } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

export default function DemoNotice() {
  const isDemo = !import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY === 'your-anon-key-here';

  if (!isDemo) return null;

  return (
    <Alert className="mb-6 border-orange-200 bg-orange-50">
      <AlertTriangle className="h-4 w-4 text-orange-600" />
      <AlertTitle className="text-orange-800">Demo Mode - Database Not Connected</AlertTitle>
      <AlertDescription className="text-orange-700">
        <p className="mb-3">
          The backend system is ready, but you need to set up your Supabase database to use the full functionality.
          Authentication and data storage require a configured database connection.
        </p>
        <div className="bg-orange-100 rounded-md p-3 mb-3 text-sm">
          <p className="font-medium mb-1">Quick Setup Steps:</p>
          <ol className="list-decimal list-inside space-y-1 text-orange-800">
            <li>Create a Supabase project</li>
            <li>Copy your project URL and anon key</li>
            <li>Update the .env file with your credentials</li>
            <li>Run the database schema</li>
          </ol>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://github.com/user/manechem/blob/main/SETUP_GUIDE.md', '_blank')}
            className="border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View Setup Guide
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://supabase.com/dashboard', '_blank')}
            className="border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Go to Supabase
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
