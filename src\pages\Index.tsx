import Hero from "@/components/Hero";
import SEO from "@/components/SEO";
import DemoNotice from "@/components/DemoNotice";
import { Card, CardContent } from "@/components/ui/card";
import industriesImg from "@/assets/industries-montage.jpg";
import sustainImg from "@/assets/sustainability-green-plant.jpg";
import { ShieldCheck, Truck, Globe2 } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Index = () => {
  return (
    <main>
      <SEO
        title="Manechem | Chemical Distribution & Supply"
        description="Trusted chemical distributor for pharma, food, and industrial markets. Safe, compliant logistics with SDS/COA and full traceability."
        canonical="/"
        jsonLd={{
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'Manechem',
          url: '/',
          logo: '/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png',
          sameAs: [],
        }}
      />

      <Hero
        title="Safe, compliant chemical supply—delivered on time"
        subtitle="From audited global producers to your line with full batch traceability, regulatory labeling, and returnable packaging options."
      />

      <section className="container py-16">
        <DemoNotice />
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Why Choose Manechem?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We deliver more than chemicals—we provide peace of mind through proven reliability,
            comprehensive compliance, and unwavering commitment to safety.
          </p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {[{
            icon: Truck,
            title: 'Reliable logistics',
            desc: 'ADR/HAZMAT-compliant network with last‑mile visibility.',
            color: 'bg-blue-50 text-blue-600 border-blue-200'
          },{
            icon: ShieldCheck,
            title: 'Quality you can prove',
            desc: 'ISO 9001 QMS, COA & SDS linked to every batch.',
            color: 'bg-green-50 text-green-600 border-green-200'
          },{
            icon: Globe2,
            title: 'Global sourcing',
            desc: 'Audited supply base with multi‑sourcing for resilience.',
            color: 'bg-orange-50 text-orange-600 border-orange-200'
          }].map(({icon: Icon, title, desc, color}) => (
            <Card key={title} className="p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-lg ${color} group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{title}</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">{desc}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>

      <section className="container py-16">
        <div className="grid gap-8 md:grid-cols-2">
          <Card className="overflow-hidden group hover:shadow-xl transition-all duration-500">
            <div className="relative overflow-hidden">
              <img
                src={industriesImg}
                alt="Industries montage"
                className="h-64 w-full object-cover group-hover:scale-105 transition-transform duration-500"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm font-medium text-primary">INDUSTRIES</span>
              </div>
              <h2 className="mb-3 text-2xl font-semibold">Industries we serve</h2>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                Pharma & biotech, food & beverage, personal care, coatings, automotive, and more.
              </p>
              <div className="flex items-center justify-between">
                <Link to="/industries">
                  <Button variant="outlinePrimary" className="hover:shadow-md transition-all duration-300">
                    Learn more
                  </Button>
                </Link>
                <div className="text-sm text-muted-foreground">8+ sectors</div>
              </div>
            </CardContent>
          </Card>

          <Card className="overflow-hidden group hover:shadow-xl transition-all duration-500">
            <div className="relative overflow-hidden">
              <img
                src={sustainImg}
                alt="Sustainability visual"
                className="h-64 w-full object-cover group-hover:scale-105 transition-transform duration-500"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium text-green-600">SUSTAINABILITY</span>
              </div>
              <h2 className="mb-3 text-2xl font-semibold">Sustainability in practice</h2>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                Lower‑carbon logistics and returnable packaging to reduce waste and risk.
              </p>
              <div className="flex items-center justify-between">
                <Link to="/sustainability">
                  <Button variant="outlinePrimary" className="hover:shadow-md transition-all duration-300">
                    Our approach
                  </Button>
                </Link>
                <div className="text-sm text-muted-foreground">Carbon neutral</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-accent/5 py-16">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Trusted by Industry Leaders</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our commitment to excellence has earned the trust of companies worldwide,
              delivering consistent results across diverse industries.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-4">
            {[
              { number: "500+", label: "Chemical Products", icon: "🧪" },
              { number: "99.9%", label: "On-time Delivery", icon: "🚚" },
              { number: "50+", label: "Countries Served", icon: "🌍" },
              { number: "24/7", label: "Customer Support", icon: "💬" }
            ].map(({ number, label, icon }) => (
              <div key={label} className="text-center group">
                <div className="bg-background rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300 group-hover:-translate-y-1">
                  <div className="text-3xl mb-3">{icon}</div>
                  <div className="text-3xl font-bold text-primary mb-2">{number}</div>
                  <div className="text-sm text-muted-foreground font-medium">{label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container py-16">
        <Card className="bg-gradient-to-r from-accent to-accent/80 text-accent-foreground relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/10 rounded-full blur-3xl -translate-y-32 translate-x-32" aria-hidden />
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-primary/5 rounded-full blur-2xl translate-y-24 -translate-x-24" aria-hidden />

          <CardContent className="relative z-10 flex flex-col items-start gap-6 p-8 md:flex-row md:items-center md:justify-between">
            <div className="space-y-3">
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-primary/20 text-primary-foreground text-xs font-medium rounded-full">
                <div className="w-1.5 h-1.5 bg-primary-foreground rounded-full animate-pulse"></div>
                READY TO HELP
              </div>
              <h2 className="text-3xl font-bold">Need an SDS or a quote?</h2>
              <p className="text-accent-foreground/80 max-w-md leading-relaxed">
                Tell us your material, packaging, and cadence—we'll tailor a program that meets your exact requirements.
              </p>
              <div className="flex items-center gap-4 text-sm text-accent-foreground/70">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Response within 2 hours</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span>Custom solutions</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <Link to="/sds" className="w-full sm:w-auto">
                <Button variant="outline" className="w-full bg-background/10 border-accent-foreground/20 text-accent-foreground hover:bg-background/20">
                  Find SDS
                </Button>
              </Link>
              <Link to="/dashboard" className="w-full sm:w-auto">
                <Button variant="hero" className="w-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  Business System
                </Button>
              </Link>
              <Link to="/contact" className="w-full sm:w-auto">
                <Button variant="outline" className="w-full bg-background/10 border-accent-foreground/20 text-accent-foreground hover:bg-background/20">
                  Request quote
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Business Management System Section */}
      <section className="bg-primary/5 py-16">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Complete Business Management System</h2>
            <p className="text-lg text-muted-foreground mb-8">
              Streamline your chemical distribution business with our integrated management platform.
              Handle invoices, quotations, purchase orders, and inventory all in one place.
            </p>

            <div className="grid gap-6 md:grid-cols-3 mb-8">
              <div className="bg-background rounded-lg p-6 shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  📄
                </div>
                <h3 className="font-semibold mb-2">Document Management</h3>
                <p className="text-sm text-muted-foreground">
                  Create and manage quotations, invoices, purchase orders, and payment vouchers
                </p>
              </div>

              <div className="bg-background rounded-lg p-6 shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  📊
                </div>
                <h3 className="font-semibold mb-2">Business Analytics</h3>
                <p className="text-sm text-muted-foreground">
                  Track sales, monitor inventory, and generate comprehensive business reports
                </p>
              </div>

              <div className="bg-background rounded-lg p-6 shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  🔒
                </div>
                <h3 className="font-semibold mb-2">Secure & Compliant</h3>
                <p className="text-sm text-muted-foreground">
                  Role-based access control with full audit trails and data security
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/auth">
                <Button size="lg" variant="hero" className="shadow-lg hover:shadow-xl transition-all duration-300">
                  Access Business System
                </Button>
              </Link>
              <Link to="/contact">
                <Button size="lg" variant="outline">
                  Request Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default Index;
